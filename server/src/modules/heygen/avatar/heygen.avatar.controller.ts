import express, { Express, Request, Response } from "express";
import axios from "axios";
import { decodeAccess } from "../../../middleware/decodeAccess.mw";
import multer from "multer";

const upload = multer();

export class HeyGenAvatarController {
    private router = express.Router();

    constructor() {
        // Create custom avatar endpoint
        this.router.post(
            "/create-custom",
            [
                decodeAccess,
                upload.single('image')
            ],
            (request: Request, response: Response) => {
                return this.createCustomAvatar(request, response);
            }
        );

        // Get my avatars endpoint
        this.router.get(
            "/my-avatars",
            [decodeAccess],
            (request: Request, response: Response) => {
                return this.getMyAvatars(request, response);
            }
        );
    }

    public use(expressServer: Express, path: string): void {
        expressServer.use(path, this.router);
    }

    private async createCustomAvatar(request: Request, response: Response): Promise<Response> {
        try {
            if (!request.file) {
                return response.status(400).json({
                    error: "No image file provided"
                });
            }

            const HEYGEN_API_KEY = "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ==";
            const uploadedImage = request.file;

            // Step 1: Upload user's image to HeyGen's Asset Upload API to get image_key
            const fileBuffer = uploadedImage.buffer;

            const uploadResponse = await axios.post('https://upload.heygen.com/v1/asset', fileBuffer, {
                headers: {
                    'X-Api-Key': HEYGEN_API_KEY,
                    'Content-Type': uploadedImage.mimetype
                }
            });

            if (!uploadResponse.data?.data?.id) {
                return response.status(500).json({
                    error: "Failed to upload image to HeyGen"
                });
            }

            const assetId = uploadResponse.data.data.id;

            // Step 1.5: Poll asset upload status until completed
            const pollAssetStatus = async (): Promise<string> => {
                const maxAttempts = 30; // 5 minutes with 10-second intervals
                let attempts = 0;

                return new Promise((resolve, reject) => {
                    const checkStatus = async () => {
                        try {
                            const statusResponse = await axios.get(`https://api.heygen.com/v1/asset/${assetId}`, {
                                headers: {
                                    'X-Api-Key': HEYGEN_API_KEY
                                }
                            });

                            const { status, image_key } = statusResponse.data.data;

                            if (status === "completed" && image_key) {
                                resolve(image_key);
                            } else if (status === "failed") {
                                reject(new Error("Asset upload failed"));
                            } else if (status === "pending" || status === "processing") {
                                attempts++;
                                if (attempts < maxAttempts) {
                                    setTimeout(checkStatus, 10000); // Check every 10 seconds
                                } else {
                                    reject(new Error("Asset upload timed out"));
                                }
                            }
                        } catch (err) {
                            reject(err);
                        }
                    };

                    checkStatus();
                });
            };

            const imageKey = await pollAssetStatus();

            // Step 4: Create avatar group using the image_key from uploaded photo
            const createGroupResponse = await axios.post('https://api.heygen.com/v2/photo_avatar/avatar_group/create', {
                name: `Custom Group ${Date.now()}`,
                image_key: imageKey
            }, {
                headers: {
                    'X-Api-Key': HEYGEN_API_KEY,
                    'Content-Type': 'application/json'
                }
            });

            if (!createGroupResponse.data?.data?.group_id) {
                return response.status(500).json({
                    error: "Failed to create avatar group"
                });
            }

            const groupId = createGroupResponse.data.data.group_id;

            // Step 4.5: Wait for avatar group to be ready (status: completed)
            const pollGroupStatus = async (): Promise<void> => {
                const maxAttempts = 30; // 5 minutes with 10-second intervals
                let attempts = 0;

                return new Promise((resolve, reject) => {
                    const checkStatus = async () => {
                        try {
                            const statusResponse = await axios.get(`https://api.heygen.com/v2/photo_avatar/train/status/${groupId}`, {
                                headers: {
                                    'X-Api-Key': HEYGEN_API_KEY
                                }
                            });

                            const { status } = statusResponse.data.data;

                            if (status === "completed") {
                                resolve();
                            } else if (status === "failed") {
                                reject(new Error("Avatar group creation failed"));
                            } else if (status === "pending" || status === "processing") {
                                attempts++;
                                if (attempts < maxAttempts) {
                                    setTimeout(checkStatus, 10000); // Check every 10 seconds
                                } else {
                                    reject(new Error("Avatar group creation timed out"));
                                }
                            }
                        } catch (err) {
                            reject(err);
                        }
                    };

                    checkStatus();
                });
            };

            await pollGroupStatus();

            // Step 5: Train the avatar group
            const trainResponse = await axios.post('https://api.heygen.com/v2/photo_avatar/train', {
                group_id: groupId
            }, {
                headers: {
                    'X-Api-Key': HEYGEN_API_KEY,
                    'Content-Type': 'application/json'
                }
            });

            if (trainResponse.status !== 200) {
                return response.status(500).json({
                    error: "Failed to start avatar training"
                });
            }

            // Step 6: Poll for training completion
            const pollTrainingStatus = async (): Promise<void> => {
                const maxAttempts = 60; // 10 minutes with 10-second intervals
                let attempts = 0;

                return new Promise((resolve, reject) => {
                    const checkStatus = async () => {
                        try {
                            const statusResponse = await axios.get(`https://api.heygen.com/v2/photo_avatar/train/status/${groupId}`, {
                                headers: {
                                    'X-Api-Key': HEYGEN_API_KEY
                                }
                            });

                            const { status } = statusResponse.data.data;

                            if (status === "completed") {
                                resolve();
                            } else if (status === "failed") {
                                reject(new Error("Avatar training failed"));
                            } else if (status === "processing" || status === "pending") {
                                attempts++;
                                if (attempts < maxAttempts) {
                                    setTimeout(checkStatus, 10000); // Check every 10 seconds
                                } else {
                                    reject(new Error("Avatar training timed out"));
                                }
                            }
                        } catch (err) {
                            reject(err);
                        }
                    };

                    checkStatus();
                });
            };

            await pollTrainingStatus();

            // Step 7: Get avatar list from the group
            const avatarListResponse = await axios.get(`https://api.heygen.com/v2/avatar_group/${groupId}/avatars`, {
                headers: {
                    'X-Api-Key': HEYGEN_API_KEY
                }
            });

            if (!avatarListResponse.data?.data?.avatar_list?.length) {
                return response.status(500).json({
                    error: "Failed to get trained avatars"
                });
            }

            // Use the first avatar from the list
            const avatarId = avatarListResponse.data.data.avatar_list[0].id;

            return response.status(200).json({
                success: true,
                data: {
                    avatar_id: avatarId,
                    group_id: groupId,
                    message: "Custom avatar created successfully"
                }
            });

        } catch (error) {
            console.error("Custom avatar creation error:", error);
            return response.status(500).json({
                error: "Failed to create custom avatar: " + (error instanceof Error ? error.message : "Unknown error")
            });
        }
    }

    private async getMyAvatars(request: Request, response: Response): Promise<Response> {
        try {
            const HEYGEN_API_KEY = "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ==";

            const avatarsResponse = await axios.get('https://api.heygen.com/v2/photo_avatar/avatar_groups', {
                headers: {
                    'Accept': 'application/json',
                    'X-Api-Key': HEYGEN_API_KEY
                }
            });

            return response.status(200).json({
                success: true,
                data: avatarsResponse.data.data
            });

        } catch (error) {
            console.error("Error fetching my avatars:", error);
            return response.status(500).json({
                error: "Failed to fetch my avatars: " + (error instanceof Error ? error.message : "Unknown error")
            });
        }
    }
}
