/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { ConfirmationBoxWrapper } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { useNavigate } from "react-router-dom";
import { useDropzone } from "react-dropzone";
import { getErrorString } from "../hooks/getErrorString";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import Lottie from "lottie-react";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";
import { HeyGenAvatar, HeyGenVoice, MyHeyGenAvatar } from "@src/types/heygen";
import getHeyGenAvatars from "../hooks/getHeyGenAvatars";
import getHeyGenVoices from "../hooks/getHeyGenVoices";
import getMyHeyGenAvatars from "../hooks/getMyHeyGenAvatars";
import createCustomHeyGenAvatar from "../hooks/createCustomHeyGenAvatar";
import generateHeyGenVideo from "../hooks/generateHeyGenVideo";
import getHeyGenVideoStatus from "../hooks/getHeyGenVideoStatus";

import uploadVideoURL from "../hooks/uploadVideoURL";

import styled from "styled-components";

interface Props {
    saveChanges: boolean;
    setSaveChanges: (value: boolean) => void;
    navigationUrl: string;
    showConfirmLeavingModal: boolean;
    setShowConfirmLeavingModal: (value: boolean) => void;
    hideCreateVideo: boolean;
}

// Modern styled components
const Container = styled.div`
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
`;

const Header = styled.div`
    text-align: center;
    margin-bottom: 3rem;
`;

const Title = styled.h1`
    font-size: 2.5rem;
    font-weight: 700;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 1rem;
    background: linear-gradient(135deg, ${(props) => props.theme.colors.apButton}, ${(props) => props.theme.colors.apButtonHover});
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
`;

const Subtitle = styled.p`
    font-size: 1.2rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
`;

const Section = styled.div`
    margin-bottom: 4rem;
`;

const SectionHeader = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
    font-size: 1.8rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0;
`;

const SectionDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0.5rem 0 0 0;
`;

const AvatarGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(255px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
`;

const AvatarCard = styled.div<{ selected?: boolean }>`
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 3px solid ${(props) => props.selected ? props.theme.colors.apButton : 'transparent'};
    position: relative;

    &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    ${(props) => props.selected && `
        &::before {
            content: '✓';
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: ${props.theme.colors.apButton};
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            z-index: 2;
        }
    `}
`;

const AvatarImage = styled.img`
    width: 100%;
    height: 200px;
    object-fit: cover;
`;

const AvatarInfo = styled.div`
    padding: 1.5rem;
`;

const AvatarName = styled.h3`
    font-size: 1.1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0 0 0.5rem 0;
`;

const AvatarMeta = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
`;

const AvatarGender = styled.span`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    background: ${(props) => props.theme.colors.apSectionBackground};
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
`;

const PremiumBadge = styled.span`
    font-size: 0.8rem;
    color: ${(props) => props.theme.colors.apYellow};
    background: rgba(255, 190, 43, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 500;
`;

const CustomAvatarSection = styled.div`
    background: linear-gradient(135deg, ${(props) => props.theme.colors.apSectionBackground}, #f0f8ff);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed ${(props) => props.theme.colors.apButton};
    transition: all 0.3s ease;

    &:hover {
        border-color: ${(props) => props.theme.colors.apButtonHover};
        background: linear-gradient(135deg, #f0f8ff, ${(props) => props.theme.colors.apSectionBackground});
    }
`;

const UploadIcon = styled.div`
    width: 80px;
    height: 80px;
    background: ${(props) => props.theme.colors.apButton};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
`;

const UploadTitle = styled.h3`
    font-size: 1.5rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 1rem;
`;

const UploadDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin-bottom: 2rem;
    line-height: 1.6;
`;

const ConfigurationPanel = styled.div`
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-top: 2rem;
`;

const FormGroup = styled.div`
    margin-bottom: 2rem;
`;

const Label = styled.label`
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 0.75rem;
`;

const Select = styled.select`
    width: 100%;
    padding: 1rem;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    color: ${(props) => props.theme.colors.apTextColor};
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: ${(props) => props.theme.colors.apButton};
        box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    }
`;

const TextArea = styled.textarea`
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: white;
    color: ${(props) => props.theme.colors.apTextColor};
    resize: vertical;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: ${(props) => props.theme.colors.apButton};
        box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    }

    &::placeholder {
        color: ${(props) => props.theme.colors.apOffBlack};
    }
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;

    ${(props) => props.variant === 'secondary' ? `
        background: ${props.theme.colors.apThirdButton};
        color: ${props.theme.colors.apThirdButtonColor};
        border: 2px solid ${props.theme.colors.apLowMedGrey};

        &:hover {
            background: ${props.theme.colors.apThirdButtonHover};
            border-color: ${props.theme.colors.apButton};
        }
    ` : `
        background: ${props.theme.colors.apButton};
        color: white;

        &:hover {
            background: ${props.theme.colors.apButtonHover};
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 51, 255, 0.3);
        }
    `}

    &:disabled {
        background: ${(props) => props.theme.colors.apGreyButton};
        color: ${(props) => props.theme.colors.disabledTextColor};
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
`;

const LoadingOverlay = styled.div`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
`;

const LoadingContent = styled.div`
    background: white;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
`;

const LoadingTitle = styled.h3`
    font-size: 1.5rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 1rem 0 0.5rem;
`;

const LoadingDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0;
    line-height: 1.5;
`;

const ButtonGroup = styled.div`
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
`;

const PreviewImage = styled.img`
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 12px;
    margin: 1rem auto;
    display: block;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
`;

const SelectedAvatarDisplay = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    background: ${(props) => props.theme.colors.apSectionBackground};
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
`;

const SelectedAvatarImage = styled.img`
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
`;

const SelectedAvatarInfo = styled.div`
    flex: 1;
`;

const SelectedAvatarName = styled.h4`
    font-size: 1.1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0 0 0.25rem 0;
`;

const SelectedAvatarMeta = styled.p`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0;
`;

const CharacterCount = styled.div`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    text-align: right;
    margin-top: 0.5rem;
`;

const VideoAvatarLibrary: React.FC<Props> = ({
    navigationUrl,
    saveChanges,
    setSaveChanges,
    showConfirmLeavingModal,
    setShowConfirmLeavingModal,
    hideCreateVideo
}) => {
    const translation = useTranslation();
    const { apiRetryHandler } = useTokenCheck();
    const navigate = useNavigate();

    // State management
    const [avatars, setAvatars] = useState<HeyGenAvatar[]>([]);
    const [myAvatars, setMyAvatars] = useState<MyHeyGenAvatar[]>([]);
    const [voices, setVoices] = useState<HeyGenVoice[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");
    const [selectedAvatar, setSelectedAvatar] = useState<HeyGenAvatar | null>(null);
    const [selectedMyAvatar, setSelectedMyAvatar] = useState<MyHeyGenAvatar | null>(null);
    const [selectedVoice, setSelectedVoice] = useState<string>("");
    const [inputText, setInputText] = useState("");
    const [isGenerating, setIsGenerating] = useState(false);
    const [uploadedImage, setUploadedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>("");
    const [customTalkingPhotoId, setCustomTalkingPhotoId] = useState<string>("");
    const [showConfiguration, setShowConfiguration] = useState(false);

    // Image upload dropzone
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        accept: {
            'image/*': ['.jpeg', '.jpg', '.png']
        },
        maxFiles: 1,
        onDrop: (acceptedFiles) => {
            if (acceptedFiles.length > 0) {
                const file = acceptedFiles[0];
                setUploadedImage(file);

                // Create preview
                const reader = new FileReader();
                reader.onload = () => {
                    setImagePreview(reader.result as string);
                };
                reader.readAsDataURL(file);

                setShowConfiguration(true);
            }
        }
    });

    // Fetch data on component mount
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch avatars, my avatars, and voices in parallel
                const [avatarsResponse, myAvatarsResponse, voicesResponse] = await Promise.all([
                    apiRetryHandler(() => getHeyGenAvatars()),
                    apiRetryHandler(() => getMyHeyGenAvatars()),
                    apiRetryHandler(() => getHeyGenVoices())
                ]);

                if (avatarsResponse.error) {
                    setError(getErrorString(translation, avatarsResponse.error?.response?.data?.error));
                } else if (avatarsResponse.data) {
                    setAvatars(avatarsResponse.data.avatars || []);
                }

                if (myAvatarsResponse.error) {
                    console.warn("Failed to load my avatars:", myAvatarsResponse.error);
                } else if (myAvatarsResponse.data) {
                    setMyAvatars(myAvatarsResponse.data.avatar_list || []);
                }

                if (voicesResponse.error) {
                    console.warn("Failed to load voices:", voicesResponse.error);
                } else if (voicesResponse.data) {
                    setVoices(voicesResponse.data.voices || []);
                    // Set default voice
                    if (voicesResponse.data.voices.length > 0) {
                        setSelectedVoice(voicesResponse.data.voices[0].voice_id);
                    }
                }
            } catch (err) {
                setError("Failed to load data");
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Handle public avatar selection
    const handleAvatarSelect = (avatar: HeyGenAvatar) => {
      console.log(avatar);
        setSelectedAvatar(avatar);
        setSelectedMyAvatar(null); // Clear my avatar selection
        setShowConfiguration(true);
        setUploadedImage(null);
        setImagePreview("");
        setCustomTalkingPhotoId("");
    };

    // Handle my avatar selection
    const handleMyAvatarSelect = (myAvatar: MyHeyGenAvatar) => {

      console.log(myAvatar);
        setSelectedMyAvatar(myAvatar);
        setSelectedAvatar(null); // Clear public avatar selection
        setShowConfiguration(true);
        setUploadedImage(null);
        setImagePreview("");
        setCustomTalkingPhotoId("");
    };

    // Handle custom avatar creation using server-side API
    const handleCreateCustomAvatar = async () => {
        if (!uploadedImage) return;

        try {
            setIsGenerating(true);

            // Call server-side endpoint to create custom avatar
            const { data: createResponse, error: createError } = await apiRetryHandler(
                () => createCustomHeyGenAvatar(uploadedImage)
            );

            if (createError || !createResponse?.success) {
                setError("Failed to create custom avatar");
                return;
            }

            // Set the avatar ID for video generation
            setCustomTalkingPhotoId(createResponse.data.avatar_id);

            // Refresh My Avatars list to show the newly created avatar
            const refreshMyAvatars = async () => {
                try {
                    const myAvatarsResponse = await apiRetryHandler(() => getMyHeyGenAvatars());
                    if (myAvatarsResponse.data) {
                        setMyAvatars(myAvatarsResponse.data.avatar_groups || []);
                    }
                } catch (err) {
                    console.warn("Failed to refresh my avatars:", err);
                }
            };

            await refreshMyAvatars();

        } catch (err) {
            console.error("Custom avatar creation error:", err);
            setError("Failed to create custom avatar: " + (err instanceof Error ? err.message : "Unknown error"));
        } finally {
            setIsGenerating(false);
        }
    };

    // Handle video generation and submission
    const handleGenerateVideo = async () => {
        if (!inputText.trim() || !selectedVoice) {
            setError("Please enter text and select a voice");
            return;
        }

        if (!selectedAvatar && !selectedMyAvatar && !customTalkingPhotoId) {
            setError("Please select an avatar or upload a custom image");
            return;
        }

        try {
            setIsGenerating(true);
            setSaveChanges(true);

            // Prepare video generation request
            const getVideoTitle = () => {
                if (selectedAvatar) return `Avatar Video - ${selectedAvatar.avatar_name}`;
                if (selectedMyAvatar) return `My Avatar Video - ${selectedMyAvatar.name}`;
                return "Custom Avatar Video";
            };

            const getCharacterConfig = () => {
                if (selectedAvatar) {
                    return {
                        type: "avatar" as const,
                        avatar_id: selectedAvatar.avatar_id,
                        scale: 1.0,
                        avatar_style: "normal" as const
                    };
                }
                if (selectedMyAvatar) {
                    return {
                        type: "avatar" as const,
                        avatar_id: selectedMyAvatar.id,
                        scale: 1.0,
                        avatar_style: "normal" as const
                    };
                }
                // Custom uploaded avatar
                return {
                    type: "avatar" as const,
                    avatar_id: customTalkingPhotoId,
                    scale: 1.0,
                    avatar_style: "normal" as const
                };
            };

            const videoRequest = {
                title: getVideoTitle(),
                mode: selectedAvatar ? "studio" : "talking_photo",
                video_inputs: [
                    {
                        character: getCharacterConfig(),
                        voice: {
                            type: "text" as const,
                            voice_id: selectedVoice,
                            input_text: inputText.trim(),
                            speed: 1.0
                        },
                        background: {
                            type: "color" as const,
                            value: "#f6f6fc"
                        }
                    }
                ],
                dimension: {
                    width: 480,
                    height: 640
                },
                caption: false
            };

            // Generate video
            const { data: videoData, error: videoError } = await apiRetryHandler(
                () => generateHeyGenVideo(videoRequest)
            );

            if (videoError || !videoData?.data) {
                setError("Failed to generate video");
                return;
            }

            const videoId = videoData.data.video_id;

            // Poll for video completion
            await pollVideoStatus(videoId);

        } catch (err) {
            setError("Failed to generate video");
        } finally {
            setIsGenerating(false);
            setSaveChanges(false);
        }
    };

    // Poll video status and submit to API when ready
    const pollVideoStatus = async (videoId: string) => {
        const maxAttempts = 60; // 5 minutes with 5-second intervals
        let attempts = 0;

        const checkStatus = async () => {
            try {
                const { data: statusData, error: statusError } = await apiRetryHandler(
                    () => getHeyGenVideoStatus(videoId)
                );

                if (statusError || !statusData?.data) {
                    setError("Failed to check video status");
                    return;
                }

                const { status, video_url } = statusData.data;

                if (status === "completed" && video_url) {
                    // Submit video URL to our API
                    const { error: uploadError } = await apiRetryHandler(
                        () => uploadVideoURL(video_url)
                    );

                    if (uploadError) {
                        setError("Failed to save video");
                    } else {
                        // Navigate to video library
                        navigate("/video-library");
                    }

                } else if (status === "waiting" || status === "processing") {
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(checkStatus, 5000);
                    } else {
                        setError("Video generation timed out");
                    }
                }
            } catch (err) {
                setError("Failed to check video status");
            }
        };

        checkStatus();
    };

    // Reset configuration
    const handleReset = () => {
        setSelectedAvatar(null);
        setSelectedMyAvatar(null);
        setUploadedImage(null);
        setImagePreview("");
        setCustomTalkingPhotoId("");
        setInputText("");
        setShowConfiguration(false);
        setError("");
    };

    if (loading) {
        return (
            <Container>
                <LoadingContent>
                    <Lottie
                        animationData={LoadingSpinnerAnimation}
                        style={{ width: 100, height: 100 }}
                        loop={true}
                    />
                    <LoadingTitle>Loading Avatar Library</LoadingTitle>
                    <LoadingDescription>Please wait while we fetch the available avatars and voices...</LoadingDescription>
                </LoadingContent>
            </Container>
        );
    }

    return (
        <Container>
            {error && <ErrorMessage error={error} setError={setError} displayCloseIcon={true} />}

            {/* Public Avatars Section */}
            <Section>
                <SectionHeader>
                    <div>
                        <SectionTitle>Choose Your Avatar</SectionTitle>
                        <SectionDescription>Select from our collection of professional AI avatars</SectionDescription>
                    </div>
                </SectionHeader>

                <AvatarGrid>
                    {avatars.map((avatar) => (
                        <AvatarCard
                            key={avatar.avatar_id}
                            selected={selectedAvatar?.avatar_id === avatar.avatar_id}
                            onClick={() => handleAvatarSelect(avatar)}
                        >
                            <AvatarImage
                                src={avatar.preview_image_url}
                                alt={avatar.avatar_name}
                                onError={(e) => {
                                    (e.target as HTMLImageElement).src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkF2YXRhcjwvdGV4dD48L3N2Zz4=";
                                }}
                            />
                            <AvatarInfo>
                                <AvatarName>{avatar.avatar_name}</AvatarName>
                                <AvatarMeta>
                                    <AvatarGender>{avatar.gender}</AvatarGender>
                                    <PremiumBadge>Premium</PremiumBadge>
                                </AvatarMeta>
                            </AvatarInfo>
                        </AvatarCard>
                    ))}
                </AvatarGrid>
            </Section>

            {/* My Avatars Section */}
            {myAvatars.length > 0 && (
                <Section>
                    <SectionHeader>
                        <div>
                            <SectionTitle>My Avatars</SectionTitle>
                            <SectionDescription>Your custom created avatars</SectionDescription>
                        </div>
                    </SectionHeader>

                    <AvatarGrid>
                        {myAvatars.map((myAvatar) => (
                            <AvatarCard
                                key={myAvatar.id}
                                selected={selectedMyAvatar?.id === myAvatar.id}
                                onClick={() => handleMyAvatarSelect(myAvatar)}
                            >
                                <AvatarImage
                                    src={myAvatar.image_url}
                                    alt={myAvatar.name}
                                    onError={(e) => {
                                        (e.target as HTMLImageElement).src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk15IEF2YXRhcjwvdGV4dD48L3N2Zz4=";
                                    }}
                                />
                                <AvatarInfo>
                                    <AvatarName>{myAvatar.name}</AvatarName>
                                    <AvatarMeta>
                                        <AvatarGender>{myAvatar.status}</AvatarGender>
                                        <PremiumBadge>Custom</PremiumBadge>
                                    </AvatarMeta>
                                </AvatarInfo>
                            </AvatarCard>
                        ))}
                    </AvatarGrid>
                </Section>
            )}

            {/* Custom Avatar Upload Section */}
            <Section>
                <SectionHeader>
                    <div>
                        <SectionTitle>Create Custom Avatar</SectionTitle>
                        <SectionDescription>Upload your photo to create a personalized talking avatar</SectionDescription>
                    </div>
                </SectionHeader>

                <CustomAvatarSection {...getRootProps()}>
                    <input {...getInputProps()} />
                    <UploadIcon>📷</UploadIcon>
                    <UploadTitle>
                        {isDragActive ? "Drop your image here" : "Upload Your Photo"}
                    </UploadTitle>
                    <UploadDescription>
                        Drag and drop an image here, or click to select a file.<br />
                        Supported formats: JPG, PNG (max 10MB)
                    </UploadDescription>
                    {imagePreview && (
                        <PreviewImage src={imagePreview} alt="Preview" />
                    )}
                </CustomAvatarSection>
            </Section>

            {/* Configuration Panel */}
            {showConfiguration && (
                <ConfigurationPanel>
                    <SectionTitle>Configure Your Video</SectionTitle>

                    {/* Selected Avatar Display */}
                    {selectedAvatar && (
                        <SelectedAvatarDisplay>
                            <SelectedAvatarImage src={selectedAvatar.preview_image_url} alt={selectedAvatar.avatar_name} />
                            <SelectedAvatarInfo>
                                <SelectedAvatarName>{selectedAvatar.avatar_name}</SelectedAvatarName>
                                <SelectedAvatarMeta>{selectedAvatar.gender} • Premium</SelectedAvatarMeta>
                            </SelectedAvatarInfo>
                        </SelectedAvatarDisplay>
                    )}

                    {/* Selected My Avatar Display */}
                    {selectedMyAvatar && (
                        <SelectedAvatarDisplay>
                            <SelectedAvatarImage src={selectedMyAvatar.image_url} alt={selectedMyAvatar.name} />
                            <SelectedAvatarInfo>
                                <SelectedAvatarName>{selectedMyAvatar.name}</SelectedAvatarName>
                                <SelectedAvatarMeta>Custom Avatar • {selectedMyAvatar.status}</SelectedAvatarMeta>
                            </SelectedAvatarInfo>
                        </SelectedAvatarDisplay>
                    )}

                    {/* Custom Avatar Display */}
                    {uploadedImage && (
                        <SelectedAvatarDisplay>
                            <SelectedAvatarImage src={imagePreview} alt="Custom Avatar" />
                            <SelectedAvatarInfo>
                                <SelectedAvatarName>Custom Avatar</SelectedAvatarName>
                                <SelectedAvatarMeta>
                                    {customTalkingPhotoId ? "Ready for video generation" : "Processing..."}
                                </SelectedAvatarMeta>
                            </SelectedAvatarInfo>
                        </SelectedAvatarDisplay>
                    )}

                    {/* Voice Selection */}
                    <FormGroup>
                        <Label>Select Voice</Label>
                        <Select
                            value={selectedVoice}
                            onChange={(e) => setSelectedVoice(e.target.value)}
                        >
                            <option value="">Choose a voice...</option>
                            {voices.map((voice) => (
                                <option key={voice.voice_id} value={voice.voice_id}>
                                    {voice.name} ({voice.gender}, {voice.language})
                                </option>
                            ))}
                        </Select>
                    </FormGroup>

                    {/* Text Input */}
                    <FormGroup>
                        <Label>Enter Your Text</Label>
                        <TextArea
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            placeholder="Enter the text you want your avatar to speak..."
                            rows={4}
                        />
                        <CharacterCount>{inputText.length}/500 characters</CharacterCount>
                    </FormGroup>

                    {/* Action Buttons */}
                    {isGenerating && (
                        <LoadingOverlay>
                            <Lottie
                                animationData={LoadingSpinnerAnimation}
                                style={{ width: 80, height: 80 }}
                                loop={true}
                            />
                            <LoadingTitle>
                                {customTalkingPhotoId ? "Generating Video..." : "Creating Custom Avatar..."}
                            </LoadingTitle>
                            <LoadingDescription>
                                {customTalkingPhotoId
                                    ? "Please wait while we generate your video. This may take a few minutes."
                                    : "Please wait while we create your custom avatar. This may take several minutes."
                                }
                            </LoadingDescription>
                        </LoadingOverlay>
                    )}

                    <ButtonGroup>
                        <ActionButton variant="secondary" onClick={handleReset}>
                            Reset
                        </ActionButton>
                        {uploadedImage && !customTalkingPhotoId && (
                            <ActionButton onClick={handleCreateCustomAvatar} disabled={isGenerating}>
                                {isGenerating ? "Creating..." : "Create Avatar"}
                            </ActionButton>
                        )}
                        <ActionButton
                            onClick={handleGenerateVideo}
                            disabled={isGenerating || !inputText.trim() || !selectedVoice || ((!selectedAvatar && !selectedMyAvatar) && !customTalkingPhotoId)}
                        >
                            {isGenerating ? "Generating..." : "Generate Video"}
                        </ActionButton>
                    </ButtonGroup>
                </ConfigurationPanel>
            )}
        </Container>
    );
};

export default VideoAvatarLibrary;
