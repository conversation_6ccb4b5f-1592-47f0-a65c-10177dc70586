/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { ConfirmationBoxWrapper } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { useNavigate } from "react-router-dom";
import { useDropzone } from "react-dropzone";
import { getErrorString } from "../hooks/getErrorString";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import Lottie from "lottie-react";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";
import { HeyGenAvatar, HeyGenVoice, MyHeyGenAvatar } from "@src/types/heygen";
import getHeyGenAvatars from "../hooks/getHeyGenAvatars";
import getHeyGenVoices from "../hooks/getHeyGenVoices";
import getMyHeyGenAvatars from "../hooks/getMyHeyGenAvatars";
import generateHeyGenVideo from "../hooks/generateHeyGenVideo";
import getHeyGenVideoStatus from "../hooks/getHeyGenVideoStatus";

import uploadVideoURL from "../hooks/uploadVideoURL";
import axios from "axios";
import styled from "styled-components";

interface Props {
    saveChanges: boolean;
    setSaveChanges: (value: boolean) => void;
    navigationUrl: string;
    showConfirmLeavingModal: boolean;
    setShowConfirmLeavingModal: (value: boolean) => void;
    hideCreateVideo: boolean;
}

// Modern styled components
const Container = styled.div`
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
`;

const Header = styled.div`
    text-align: center;
    margin-bottom: 3rem;
`;

const Title = styled.h1`
    font-size: 2.5rem;
    font-weight: 700;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 1rem;
    background: linear-gradient(135deg, ${(props) => props.theme.colors.apButton}, ${(props) => props.theme.colors.apButtonHover});
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
`;

const Subtitle = styled.p`
    font-size: 1.2rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
`;

const Section = styled.div`
    margin-bottom: 4rem;
`;

const SectionHeader = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
    font-size: 1.8rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0;
`;

const SectionDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0.5rem 0 0 0;
`;

const AvatarGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(255px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
`;

const AvatarCard = styled.div<{ selected?: boolean }>`
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 3px solid ${(props) => props.selected ? props.theme.colors.apButton : 'transparent'};
    position: relative;

    &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    ${(props) => props.selected && `
        &::before {
            content: '✓';
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: ${props.theme.colors.apButton};
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            z-index: 2;
        }
    `}
`;

const AvatarImage = styled.img`
    width: 100%;
    height: 200px;
    object-fit: cover;
`;

const AvatarInfo = styled.div`
    padding: 1.5rem;
`;

const AvatarName = styled.h3`
    font-size: 1.1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0 0 0.5rem 0;
`;

const AvatarMeta = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
`;

const AvatarGender = styled.span`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    background: ${(props) => props.theme.colors.apSectionBackground};
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
`;

const PremiumBadge = styled.span`
    font-size: 0.8rem;
    color: ${(props) => props.theme.colors.apYellow};
    background: rgba(255, 190, 43, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 500;
`;

const CustomAvatarSection = styled.div`
    background: linear-gradient(135deg, ${(props) => props.theme.colors.apSectionBackground}, #f0f8ff);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed ${(props) => props.theme.colors.apButton};
    transition: all 0.3s ease;

    &:hover {
        border-color: ${(props) => props.theme.colors.apButtonHover};
        background: linear-gradient(135deg, #f0f8ff, ${(props) => props.theme.colors.apSectionBackground});
    }
`;

const UploadIcon = styled.div`
    width: 80px;
    height: 80px;
    background: ${(props) => props.theme.colors.apButton};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
`;

const UploadTitle = styled.h3`
    font-size: 1.5rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 1rem;
`;

const UploadDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin-bottom: 2rem;
    line-height: 1.6;
`;

const ConfigurationPanel = styled.div`
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-top: 2rem;
`;

const FormGroup = styled.div`
    margin-bottom: 2rem;
`;

const Label = styled.label`
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 0.75rem;
`;

const Select = styled.select`
    width: 100%;
    padding: 1rem;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    color: ${(props) => props.theme.colors.apTextColor};
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: ${(props) => props.theme.colors.apButton};
        box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    }
`;

const TextArea = styled.textarea`
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: white;
    color: ${(props) => props.theme.colors.apTextColor};
    resize: vertical;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: ${(props) => props.theme.colors.apButton};
        box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    }

    &::placeholder {
        color: ${(props) => props.theme.colors.apOffBlack};
    }
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;

    ${(props) => props.variant === 'secondary' ? `
        background: ${props.theme.colors.apThirdButton};
        color: ${props.theme.colors.apThirdButtonColor};
        border: 2px solid ${props.theme.colors.apLowMedGrey};

        &:hover {
            background: ${props.theme.colors.apThirdButtonHover};
            border-color: ${props.theme.colors.apButton};
        }
    ` : `
        background: ${props.theme.colors.apButton};
        color: white;

        &:hover {
            background: ${props.theme.colors.apButtonHover};
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 51, 255, 0.3);
        }
    `}

    &:disabled {
        background: ${(props) => props.theme.colors.apGreyButton};
        color: ${(props) => props.theme.colors.disabledTextColor};
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
`;

const LoadingOverlay = styled.div`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
`;

const LoadingContent = styled.div`
    background: white;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
`;

const LoadingTitle = styled.h3`
    font-size: 1.5rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 1rem 0 0.5rem;
`;

const LoadingDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0;
    line-height: 1.5;
`;

const ButtonGroup = styled.div`
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
`;

const PreviewImage = styled.img`
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 12px;
    margin: 1rem auto;
    display: block;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
`;

const VideoAvatarLibrary: React.FC<Props> = ({
    navigationUrl,
    saveChanges,
    setSaveChanges,
    showConfirmLeavingModal,
    setShowConfirmLeavingModal,
    hideCreateVideo
}) => {
    const translation = useTranslation();
    const { apiRetryHandler } = useTokenCheck();
    const navigate = useNavigate();

    // State management
    const [avatars, setAvatars] = useState<HeyGenAvatar[]>([]);
    const [myAvatars, setMyAvatars] = useState<MyHeyGenAvatar[]>([]);
    const [voices, setVoices] = useState<HeyGenVoice[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");
    const [selectedAvatar, setSelectedAvatar] = useState<HeyGenAvatar | null>(null);
    const [selectedMyAvatar, setSelectedMyAvatar] = useState<MyHeyGenAvatar | null>(null);
    const [selectedVoice, setSelectedVoice] = useState<string>("");
    const [inputText, setInputText] = useState("");
    const [isGenerating, setIsGenerating] = useState(false);
    const [uploadedImage, setUploadedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>("");
    const [customTalkingPhotoId, setCustomTalkingPhotoId] = useState<string>("");
    const [showConfiguration, setShowConfiguration] = useState(false);

    // Image upload dropzone
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        accept: {
            'image/*': ['.jpeg', '.jpg', '.png']
        },
        maxFiles: 1,
        onDrop: (acceptedFiles) => {
            if (acceptedFiles.length > 0) {
                const file = acceptedFiles[0];
                setUploadedImage(file);

                // Create preview
                const reader = new FileReader();
                reader.onload = () => {
                    setImagePreview(reader.result as string);
                };
                reader.readAsDataURL(file);

                setShowConfiguration(true);
            }
        }
    });

    // Fetch data on component mount
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch avatars, my avatars, and voices in parallel
                const [avatarsResponse, myAvatarsResponse, voicesResponse] = await Promise.all([
                    apiRetryHandler(() => getHeyGenAvatars()),
                    apiRetryHandler(() => getMyHeyGenAvatars()),
                    apiRetryHandler(() => getHeyGenVoices())
                ]);

                if (avatarsResponse.error) {
                    setError(getErrorString(translation, avatarsResponse.error?.response?.data?.error));
                } else if (avatarsResponse.data) {
                    setAvatars(avatarsResponse.data.avatars || []);
                }

                if (myAvatarsResponse.error) {
                    console.warn("Failed to load my avatars:", myAvatarsResponse.error);
                } else if (myAvatarsResponse.data) {
                    setMyAvatars(myAvatarsResponse.data.avatar_list || []);
                }

                if (voicesResponse.error) {
                    console.warn("Failed to load voices:", voicesResponse.error);
                } else if (voicesResponse.data) {
                    setVoices(voicesResponse.data.voices || []);
                    // Set default voice
                    if (voicesResponse.data.voices.length > 0) {
                        setSelectedVoice(voicesResponse.data.voices[0].voice_id);
                    }
                }
            } catch (err) {
                setError("Failed to load data");
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Handle public avatar selection
    const handleAvatarSelect = (avatar: HeyGenAvatar) => {
      console.log(avatar);
        setSelectedAvatar(avatar);
        setSelectedMyAvatar(null); // Clear my avatar selection
        setShowConfiguration(true);
        setUploadedImage(null);
        setImagePreview("");
        setCustomTalkingPhotoId("");
    };

    // Handle my avatar selection
    const handleMyAvatarSelect = (myAvatar: MyHeyGenAvatar) => {

      console.log(myAvatar);
        setSelectedMyAvatar(myAvatar);
        setSelectedAvatar(null); // Clear public avatar selection
        setShowConfiguration(true);
        setUploadedImage(null);
        setImagePreview("");
        setCustomTalkingPhotoId("");
    };

    // Handle custom avatar creation using HeyGen's Photo Avatars API
    const handleCreateCustomAvatar = async () => {
        if (!uploadedImage) return;

        try {
            setIsGenerating(true);

            // Step 1: Upload user's image to HeyGen's Asset Upload API to get image_key
						const arrayBuffer = await uploadedImage.arrayBuffer();
            const fileBuffer = new Uint8Array(arrayBuffer);

            const uploadResponse = await apiRetryHandler(() =>
                axios.post('https://upload.heygen.com/v1/asset', fileBuffer, {
                    headers: {
                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw==",
												'Content-Type': uploadedImage.type
                    }
                })
            );

            if (uploadResponse.error || !uploadResponse.data?.data?.id) {
                setError("Failed to upload image to HeyGen");
                return;
            }

            const assetId = uploadResponse.data.data.id;

						let imageKey = uploadResponse.data?.data?.image_key;

            //Step 1.5: Poll asset upload status until completed
            const pollAssetStatus = async (): Promise<string> => {
                const maxAttempts = 30; // 5 minutes with 10-second intervals
                let attempts = 0;

                return new Promise((resolve, reject) => {
                    const checkStatus = async () => {
                        try {
                            const statusResponse = await apiRetryHandler(() =>
                                axios.get(`https://api.heygen.com/v1/asset/${assetId}`, {
                                    headers: {
                                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
                                    }
                                })
                            );

                            if (statusResponse.error) {
                                reject(new Error("Failed to check asset upload status"));
                                return;
                            }

                            const { status, image_key } = statusResponse.data.data;

                            if (status === "completed" && image_key) {
                                resolve(image_key);
                            } else if (status === "failed") {
                                reject(new Error("Asset upload failed"));
                            } else if (status === "pending" || status === "processing") {
                                attempts++;
                                if (attempts < maxAttempts) {
                                    setTimeout(checkStatus, 10000); // Check every 10 seconds
                                } else {
                                    reject(new Error("Asset upload timed out"));
                                }
                            }
                        } catch (err) {
                            reject(err);
                        }
                    };

                    checkStatus();
                });
            };

          	if (!imageKey) {
							imageKey = await pollAssetStatus();
						}


            // Step 2: Generate AI photo avatar using HeyGen's Photo Avatars API (optional - for AI-generated avatars)
            // For now, we'll skip this step and use the uploaded image directly

            // Step 3: Use the image_key from uploaded photo directly

            // Step 4: Create avatar group using the image_key from uploaded photo
            const createGroupResponse = await apiRetryHandler(() =>
                axios.post('https://api.heygen.com/v2/photo_avatar/avatar_group/create', {
                    name: `Custom Group ${Date.now()}`,
                    image_key: imageKey
                }, {
                    headers: {
                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw==",
                        'Content-Type': 'application/json'
                    }
                })
            );

            if (createGroupResponse.error || !createGroupResponse.data?.data?.group_id) {
                setError("Failed to create avatar group");
                return;
            }

            const groupId = createGroupResponse.data.data.group_id;

            // Step 4.5: Wait for avatar group to be ready (status: completed)
            const pollGroupStatus = async (): Promise<void> => {
                const maxAttempts = 30; // 5 minutes with 10-second intervals
                let attempts = 0;

                return new Promise((resolve, reject) => {
                    const checkStatus = async () => {
                        try {
                            const statusResponse = await apiRetryHandler(() =>
                                axios.get(`https://api.heygen.com/v2/photo_avatar/avatar_group/${groupId}`, {
                                    headers: {
                                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
                                    }
                                })
                            );

                            if (statusResponse.error) {
                                reject(new Error("Failed to check group status"));
                                return;
                            }

                            const { status } = statusResponse.data.data;

                            if (status === "completed") {
                                resolve();
                            } else if (status === "failed") {
                                reject(new Error("Avatar group creation failed"));
                            } else if (status === "pending" || status === "processing") {
                                attempts++;
                                if (attempts < maxAttempts) {
                                    setTimeout(checkStatus, 10000); // Check every 10 seconds
                                } else {
                                    reject(new Error("Avatar group creation timed out"));
                                }
                            }
                        } catch (err) {
                            reject(err);
                        }
                    };

                    checkStatus();
                });
            };

						const waitForGroupReady = async (groupId: string): Promise<void> => {
							const maxAttempts = 30;
							let attempts = 0;

							return new Promise((resolve, reject) => {
								const check = async () => {
									try {
										const resp = await axios.get(
											`https://api.heygen.com/v2/photo_avatar/avatar_group/${groupId}`,
											{ headers: { "X-Api-Key": "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw==" } }
										);

										const status = resp.data?.data?.status;

										if (status === "ready") {
											resolve();
										} else if (status === "failed") {
											reject(new Error("Avatar group failed to process"));
										} else {
											attempts++;
											if (attempts < maxAttempts) {
												setTimeout(check, 5000); // Wait 5 seconds
											} else {
												reject(new Error("Avatar group processing timed out"));
											}
										}
									} catch (err) {
										reject(err);
									}
								};

								check();
							});
							};

							// const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
							// await sleep(120000);
 							// console.log("waited 5 seconds");
				    	await waitForGroupReady(groupId);

            // Step 5: Train the avatar group
            const trainResponse = await apiRetryHandler(() =>
                axios.post('https://api.heygen.com/v2/photo_avatar/train', {
                    group_id: groupId
                }, {
                    headers: {
                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw==",
                        'Content-Type': 'application/json'
                    }
                })
            );

            if (trainResponse.error) {
                setError("Failed to start avatar training");
                return;
            }

            // Step 6: Poll for training completion
            const pollTrainingStatus = async (): Promise<void> => {
                const maxAttempts = 60; // 10 minutes with 10-second intervals
                let attempts = 0;

                return new Promise((resolve, reject) => {
                    const checkStatus = async () => {
                        try {
                            const statusResponse = await apiRetryHandler(() =>
                                axios.get(`https://api.heygen.com/v2/photo_avatar/train/status/${groupId}`, {
                                    headers: {
                                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
                                    }
                                })
                            );

														console.log("statusResponse", statusResponse);

                            if (statusResponse.error) {
                                reject(new Error("Failed to check training status"));
                                return;
                            }

														console.log(statusResponse.data.data.status);
                            const { status } = statusResponse.data.data;

														console.log(status);
														console.log(" -----completed");


                            if (status === "completed" || status === "success" || status === "ready") {
                                resolve();
                            } else if (status === "failed") {
                                reject(new Error("Avatar training failed"));
                            } else if (status === "processing" || status === "in_progress" || status === "pending") {
                                attempts++;
                                if (attempts < maxAttempts) {
                                    setTimeout(checkStatus, 10000); // Check every 10 seconds
                                } else {
                                    reject(new Error("Avatar training timed out"));
                                }
                            }
                        } catch (err) {
													console.log("pollTrainingStatus error", err);
                            reject(err);
                        }
                    };

                    checkStatus();
                });
            };

            await pollTrainingStatus();

            // Step 7: Get avatar list from the group
            const avatarListResponse = await apiRetryHandler(() =>
                axios.get(`https://api.heygen.com/v2/avatar_group/${groupId}/avatars`, {
                    headers: {
                        'X-Api-Key': "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
                    }
                })
            );

            if (avatarListResponse.error || !avatarListResponse.data?.data?.avatar_list?.length) {
                setError("Failed to get trained avatars");
                return;
            }

            // Use the first avatar from the list
            const avatarId = avatarListResponse.data.data.avatar_list[0].id;
            setCustomTalkingPhotoId(avatarId);

            // Refresh My Avatars list to show the newly created avatar
            const refreshMyAvatars = async () => {
                try {
                    const myAvatarsResponse = await apiRetryHandler(() => getMyHeyGenAvatars());
                    if (myAvatarsResponse.data) {
                        setMyAvatars(myAvatarsResponse.data.avatar_groups || []);
                    }
                } catch (err) {
                    console.warn("Failed to refresh my avatars:", err);
                }
            };

            await refreshMyAvatars();

        } catch (err) {
            console.error("Custom avatar creation error:", err);
            setError("Failed to create custom avatar: " + (err instanceof Error ? err.message : "Unknown error"));
        } finally {
            setIsGenerating(false);
        }
    };

    // Handle video generation and submission
    const handleGenerateVideo = async () => {
        if (!inputText.trim() || !selectedVoice) {
            setError("Please enter text and select a voice");
            return;
        }

        if (!selectedAvatar && !selectedMyAvatar && !customTalkingPhotoId) {
            setError("Please select an avatar or upload a custom image");
            return;
        }

        try {
            setIsGenerating(true);
            setSaveChanges(true);

            // Prepare video generation request
            const getVideoTitle = () => {
                if (selectedAvatar) return `Avatar Video - ${selectedAvatar.avatar_name}`;
                if (selectedMyAvatar) return `My Avatar Video - ${selectedMyAvatar.name}`;
                return "Custom Avatar Video";
            };

            const getCharacterConfig = () => {
                if (selectedAvatar) {
                   console.log(selectedAvatar);
                    return {
                        type: "avatar" as const,
                        avatar_id: selectedAvatar.avatar_id,
                        scale: 1.0,
                        avatar_style: "normal" as const
                    };
                }
                if (selectedMyAvatar) {
                  console.log(selectedMyAvatar);
                    return {
                        type: "avatar" as const,
                        avatar_id: selectedMyAvatar.id,
                        scale: 1.0,
                        avatar_style: "normal" as const
                    };
                }
                // Custom uploaded avatar
                return {
                    type: "avatar" as const,
                    avatar_id: customTalkingPhotoId,
                    scale: 1.0,
                    avatar_style: "normal" as const
                };
            };

            const videoRequest = {
                title: getVideoTitle(),
                mode: selectedAvatar ? "studio" : "talking_photo",
                video_inputs: [
                    {
                        character: getCharacterConfig(),
                        voice: {
                            type: "text" as const,
                            voice_id: selectedVoice,
                            input_text: inputText.trim(),
                            speed: 1.0
                        },
                        background: {
                            type: "color" as const,
                            value: "#f6f6fc"
                        }
                    }
                ],
                dimension: {
                    width: 480,
                    height: 640
                },
                caption: false
            };

            // Generate video
            const { data: videoData, error: videoError } = await apiRetryHandler(
                () => generateHeyGenVideo(videoRequest)
            );

            if (videoError || !videoData?.data) {
                setError("Failed to generate video");
                return;
            }

            const videoId = videoData.data.video_id;

            // Poll for video completion
            await pollVideoStatus(videoId);

        } catch (err) {
            setError("Failed to generate video");
        } finally {
            setIsGenerating(false);
            setSaveChanges(false);
        }
    };

    // Poll video status and submit to API when ready
    const pollVideoStatus = async (videoId: string) => {
        const maxAttempts = 60; // 5 minutes with 5-second intervals
        let attempts = 0;

        const checkStatus = async (): Promise<void> => {
            try {
                const { data: statusData, error: statusError } = await apiRetryHandler(
                    () => getHeyGenVideoStatus(videoId)
                );

                if (statusError || !statusData?.data) {
                    setError("Failed to check video status");
                    return;
                }

                const { status, video_url } = statusData.data;

                if (status === "completed" && video_url) {
                    // Submit video URL to our API
                    const { error: submitError } = await apiRetryHandler(
                        () => uploadVideoURL(video_url)
                    );

                    if (submitError) {
                        setError("Failed to save video");
                        return;
                    }

                    // Redirect to video library
                    navigate("/video-library");
                    return;

                } else if (status === "failed") {
                    setError("Video generation failed");
                    return;

                } else if (status === "waiting" || status === "processing") {
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(checkStatus, 5000);
                    } else {
                        setError("Video generation timed out");
                    }
                }
            } catch (err) {
                setError("Failed to check video status");
            }
        };

        checkStatus();
    };

    // Reset configuration
    const handleReset = () => {
        setSelectedAvatar(null);
        setSelectedMyAvatar(null);
        setUploadedImage(null);
        setImagePreview("");
        setCustomTalkingPhotoId("");
        setInputText("");
        setShowConfiguration(false);
        setError("");
    };

    if (loading) {
        return (
            <Container>
                <div style={{ textAlign: "center", padding: "4rem" }}>
                    <Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "100px", margin: "auto" }} />
                    <LoadingTitle>Loading Avatars...</LoadingTitle>
                    <LoadingDescription>Please wait while we fetch the available avatars and voices.</LoadingDescription>
                </div>
            </Container>
        );
    }

    return (
        <>
            <ConfirmationBoxWrapper>
                {error && <ErrorMessage error={error} setError={setError} displayCloseIcon={true} />}
            </ConfirmationBoxWrapper>

            <Container>
                <Header>
                    <Title>AI Video Avatars</Title>
                    <Subtitle>
                        Create engaging videos with AI-powered avatars. Choose from our library or upload your own image to create a custom talking avatar.
                    </Subtitle>
                </Header>

                {/* Custom Avatar Upload Section */}
                <Section>
                    <SectionHeader>
                        <div>
                            <SectionTitle>Create Custom Avatar</SectionTitle>
                            <SectionDescription>Upload your photo to create a personalized talking avatar</SectionDescription>
                        </div>
                    </SectionHeader>

                    <CustomAvatarSection {...getRootProps()}>
                        <input {...getInputProps()} />
                        <UploadIcon>📷</UploadIcon>
                        <UploadTitle>
                            {isDragActive ? "Drop your image here" : "Upload Your Photo"}
                        </UploadTitle>
                        <UploadDescription>
                            Drag and drop an image here, or click to select a file.<br />
                            Supported formats: JPG, PNG (max 10MB)
                        </UploadDescription>
                        {imagePreview && (
                            <PreviewImage src={imagePreview} alt="Preview" />
                        )}
                    </CustomAvatarSection>
                </Section>

                {/* Avatar Library Section */}
                <Section>
                    <SectionHeader>
                        <div>
                            <SectionTitle>Avatar Library</SectionTitle>
                            <SectionDescription>Choose from our collection of professional AI avatars</SectionDescription>
                        </div>
                    </SectionHeader>

                    <AvatarGrid>
                        {avatars.map((avatar) => (
                            <AvatarCard
                                key={avatar.avatar_id}
                                selected={selectedAvatar?.avatar_id === avatar.avatar_id}
                                onClick={() => handleAvatarSelect(avatar)}
                            >
                                <AvatarImage
                                    src={avatar.preview_image_url}
                                    alt={avatar.avatar_name}
                                    onError={(e) => {
                                        (e.target as HTMLImageElement).src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkF2YXRhcjwvdGV4dD48L3N2Zz4=";
                                    }}
                                />
                                <AvatarInfo>
                                    <AvatarName>{avatar.avatar_name}</AvatarName>
                                    <AvatarMeta>
                                        <AvatarGender>{avatar.gender}</AvatarGender>
                                        {avatar.premium && <PremiumBadge>Premium</PremiumBadge>}
                                    </AvatarMeta>
                                </AvatarInfo>
                            </AvatarCard>
                        ))}
                    </AvatarGrid>
                </Section>

                {/* My Avatars Section */}
                {myAvatars.length > 0 && (
                    <Section>
                        <SectionHeader>
                            <div>
                                <SectionTitle>My Avatars</SectionTitle>
                                <SectionDescription>Your custom created avatars</SectionDescription>
                            </div>
                        </SectionHeader>

                        <AvatarGrid>
                            {myAvatars.map((myAvatar) => (
                                <AvatarCard
                                    key={myAvatar.id}
                                    selected={selectedMyAvatar?.id === myAvatar.id}
                                    onClick={() => handleMyAvatarSelect(myAvatar)}
                                >
                                    <AvatarImage
                                        src={myAvatar.image_url}
                                        alt={myAvatar.name}
                                        onError={(e) => {
                                            (e.target as HTMLImageElement).src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk15IEF2YXRhcjwvdGV4dD48L3N2Zz4=";
                                        }}
                                    />
                                    <AvatarInfo>
                                        <AvatarName>{myAvatar.name}</AvatarName>
                                        <AvatarMeta>
                                            <AvatarGender>{myAvatar.status}</AvatarGender>
                                            <PremiumBadge>Custom</PremiumBadge>
                                        </AvatarMeta>
                                    </AvatarInfo>
                                </AvatarCard>
                            ))}
                        </AvatarGrid>
                    </Section>
                )}

                {/* Configuration Panel */}
                {showConfiguration && (
                    <ConfigurationPanel>
                        <SectionTitle>Configure Your Video</SectionTitle>

                        <FormGroup>
                            <Label>Select Voice</Label>
                            <Select
                                value={selectedVoice}
                                onChange={(e) => setSelectedVoice(e.target.value)}
                            >
                                <option value="">Choose a voice...</option>
                                {voices.map((voice) => (
                                    <option key={voice.voice_id} value={voice.voice_id}>
                                        {voice.name} ({voice.language}, {voice.gender})
                                    </option>
                                ))}
                            </Select>
                        </FormGroup>

                        <FormGroup>
                            <Label>Script Text</Label>
                            <TextArea
                                value={inputText}
                                onChange={(e) => setInputText(e.target.value)}
                                placeholder="Enter the text you want your avatar to speak..."
                                maxLength={500}
                            />
                            <div style={{ textAlign: "right", fontSize: "0.9rem", color: "#666", marginTop: "0.5rem" }}>
                                {inputText.length}/500 characters
                            </div>
                        </FormGroup>

                        <ButtonGroup>
                            <ActionButton variant="secondary" onClick={handleReset}>
                                Reset
                            </ActionButton>
                            {uploadedImage && !customTalkingPhotoId && (
                                <ActionButton onClick={handleCreateCustomAvatar} disabled={isGenerating}>
                                    {isGenerating ? "Creating..." : "Create Avatar"}
                                </ActionButton>
                            )}
                            <ActionButton
                                onClick={handleGenerateVideo}
                                disabled={isGenerating || !inputText.trim() || !selectedVoice || ((!selectedAvatar && !selectedMyAvatar) && !customTalkingPhotoId)}
                            >
                                {isGenerating ? "Generating..." : "Generate Video"}
                            </ActionButton>
                        </ButtonGroup>
                    </ConfigurationPanel>
                )}
            </Container>

            {/* Loading Overlay */}
            {isGenerating && (
                <LoadingOverlay>
                    <LoadingContent>
                        <Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "80px", margin: "auto" }} />
                        <LoadingTitle>Generating Your Video</LoadingTitle>
                        <LoadingDescription>
                            Please wait while we create your AI avatar video. This may take a few minutes.
                        </LoadingDescription>
                    </LoadingContent>
                </LoadingOverlay>
            )}
        </>
    );
};

export default VideoAvatarLibrary;
