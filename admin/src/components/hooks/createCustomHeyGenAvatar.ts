import axios from "axios";

interface CreateCustomAvatarResponse {
    success: boolean;
    data: {
        avatar_id: string;
        group_id: string;
        message: string;
    };
}

const createCustomHeyGenAvatar = async (imageFile: File): Promise<{ data: CreateCustomAvatarResponse | null; error: any }> => {
    try {
        const formData = new FormData();
        formData.append('image', imageFile);

        const response = await axios.post('/api/heygen/avatars/create-custom', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error creating custom HeyGen avatar:", error);
        return { data: null, error };
    }
};

export default createCustomHeyGenAvatar;
