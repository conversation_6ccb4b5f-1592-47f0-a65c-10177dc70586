import axios from "axios";
import { HeyGenTalkingPhotoCreateRequest, HeyGenTalkingPhotoCreateResponse } from "@src/types/heygen";

const createHeyGenTalkingPhoto = async (
    request: HeyGenTalkingPhotoCreateRequest
): Promise<{ data: HeyGenTalkingPhotoCreateResponse | null; error: any }> => {
    try {
        const response = await axios.request({
            url: "https://api.heygen.com/v1/talking_photo",
            method: "POST",
            data: request,
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
                "X-Api-Key": "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error creating HeyGen talking photo:", error);
        return { data: null, error };
    }
};

export default createHeyGenTalkingPhoto;
