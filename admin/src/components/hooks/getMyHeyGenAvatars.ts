import axios from "axios";

interface MyHeyGenAvatar {
    id: string;
    name: string;
    image_url: string;
    status: string;
    created_at: number;
    group_id: string;
    is_motion: boolean;
    business_type: string;
}

interface MyHeyGenAvatarsResponse {
    avatar_list: MyHeyGenAvatar[];
}

const getMyHeyGenAvatars = async (): Promise<{ data: MyHeyGenAvatarsResponse | null; error: any }> => {
    try {
        const response = await axios.request({
            //url: "https://api.heygen.com/v2/photo_avatar/avatar_groups",
            url: "https://api.heygen.com/v2/avatar_group/b093d6cb4a70425db672a52233a87acb/avatars",
            method: "GET",
            headers: {
                Accept: "application/json",
                "X-Api-Key": "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
            }
        });

        return { data: response.data.data, error: null };
    } catch (error) {
        console.error("Error fetching My HeyGen avatars:", error);
        return { data: null, error };
    }
};

export default getMyHeyGenAvatars;
