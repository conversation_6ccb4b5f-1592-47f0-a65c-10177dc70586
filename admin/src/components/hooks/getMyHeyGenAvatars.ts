import axios from "axios";
import jwt_decode from "jwt-decode";
import { accountToken } from "@src/types/videos";

interface MyHeyGenAvatar {
    id: string;
    name: string;
    image_url: string;
    status: string;
    created_at: number;
    group_id: string;
    is_motion: boolean;
    business_type: string;
}

interface AvatarGroup {
    id: string;
    name: string;
    created_at: number;
    num_looks: number;
    preview_image: string;
    group_type: string;
    train_status: string;
    default_voice_id: string | null;
}

interface AvatarGroupListResponse {
    total_count: number;
    avatar_group_list: AvatarGroup[];
}

interface AvatarListResponse {
    avatar_list: MyHeyGenAvatar[];
}

interface MyHeyGenAvatarsResponse {
    avatar_groups: AvatarGroup[];
    avatar_list: MyHeyGenAvatar[];
}

const getMyHeyGenAvatars = async (
    HEYGEN_API_KEY: string
): Promise<{ data: MyHeyGenAvatarsResponse | null; error: any }> => {
    try {
        const accountToken = sessionStorage.getItem("token");
        if (!accountToken) {
            return { data: null, error: "No account token" };
        }

        const decoded = jwt_decode(accountToken) as accountToken;

        // Step 1: Get all avatar groups
        const groupsResponse = await axios.request({
            url: "https://api.heygen.com/v2/avatar_group.list",
            method: "GET",
            headers: {
                "Accept": "application/json",
                "X-Api-Key": HEYGEN_API_KEY
            }
        });

        const groupsData: AvatarGroupListResponse = groupsResponse.data.data;

        if (!groupsData.avatar_group_list || groupsData.avatar_group_list.length === 0) {
            return {
                data: {
                    avatar_groups: [],
                    avatar_list: []
                },
                error: null
            };
        }

        console.log(groupsData);

        // Step 2: Get avatars for each group for the user
        const allAvatars: MyHeyGenAvatar[] = [];
        const userGroups = groupsData.avatar_group_list.filter(group => group.name === decoded?.account?._id.toString());

        if (userGroups.length === 0) {
            return {
                data: {
                    avatar_groups: [],
                    avatar_list: []
                },
                error: null
            };
        }

        for (const group of userGroups) {

            try {
                const avatarsResponse = await axios.request({
                    url: `https://api.heygen.com/v2/avatar_group/${group.id}/avatars`,
                    method: "GET",
                    headers: {
                        "Accept": "application/json",
                        "X-Api-Key": HEYGEN_API_KEY
                    }
                });

                const avatarsData: AvatarListResponse = avatarsResponse.data.data;
                if (avatarsData.avatar_list && avatarsData.avatar_list.length > 0) {
                    // Add group_id to each avatar for reference
                    const avatarsWithGroupId = avatarsData.avatar_list.map(avatar => ({
                        ...avatar,
                        group_id: group.id
                    }));
                    allAvatars.push(...avatarsWithGroupId);
                }
            } catch (groupError) {
                console.warn(`Failed to fetch avatars for group ${group.id}:`, groupError);
                // Continue with other groups even if one fails
            }
        }

        return {
            data: {
                avatar_groups: groupsData.avatar_group_list,
                avatar_list: allAvatars
            },
            error: null
        };
    } catch (error) {
        console.error("Error fetching My HeyGen avatars:", error);
        return { data: null, error };
    }
};

export default getMyHeyGenAvatars;
