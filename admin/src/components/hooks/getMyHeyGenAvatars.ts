import axios from "axios";

interface MyHeyGenAvatar {
    id: string;
    name: string;
    image_url: string;
    status: string;
    created_at: number;
    group_id: string;
    is_motion: boolean;
    business_type: string;
}

interface MyHeyGenAvatarsResponse {
    avatar_list: MyHeyGenAvatar[];
}

const getMyHeyGenAvatars = async (): Promise<{ data: MyHeyGenAvatarsResponse | null; error: any }> => {
    try {
        const API_ENDPOINT = process.env.API_ENDPOINT;
        const API_VERSION = process.env.API_VERSION;
        const accessToken = localStorage.getItem("accessToken");
        const accountToken = sessionStorage.getItem("token");

        const response = await axios.request({
            url: `${API_ENDPOINT}/api/heygen/avatars/my-avatars`,
            method: "GET",
            headers: {
                "Authorization": "Bearer " + accessToken,
                "x-api-version": API_VERSION,
                "x-account-token": accountToken,
                "Accept": "application/json"
            }
        });

        return { data: response.data.data, error: null };
    } catch (error) {
        console.error("Error fetching My HeyGen avatars:", error);
        return { data: null, error };
    }
};

export default getMyHeyGenAvatars;
