import axios from "axios";
import { HeyGenAvatarsResponse } from "@src/types/heygen";

const getHeyGenAvatars = async (): Promise<{ data: HeyGenAvatarsResponse | null; error: any }> => {
	try {
		const response = await axios.request({
			url: "https://api.heygen.com/v2/avatars",
			method: "GET",
			headers: {
				Accept: "application/json",
				"X-Api-Key": "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ=="
			}
		});

		const fullData = response.data.data;
		
		const trimmedData: HeyGenAvatarsResponse = {
			avatars: fullData.avatars.slice(0, 4), // first 20 avatars
			talking_photos: fullData.talking_photos
		};

		return { data: trimmedData, error: null };
	} catch (error) {
		console.error("Error fetching HeyGen avatars:", error);
		return { data: null, error };
	}
};

export default getHeyGenAvatars;
