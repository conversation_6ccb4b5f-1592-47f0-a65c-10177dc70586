import axios from "axios";
import { HeyGenAvatarsResponse } from "@src/types/heygen";

const getHeyGenAvatars = async (
	HEYGEN_API_KEY: string
): Promise<{ data: HeyGenAvatarsResponse | null; error: any }> => {
	try {
		const response = await axios.request({
			url: "https://api.heygen.com/v2/avatars",
			method: "GET",
			headers: {
				Accept: "application/json",
				"X-Api-Key": HEYGEN_API_KEY
			}
		});

		const fullData = response.data.data;
		
		const trimmedData: HeyGenAvatarsResponse = {
			avatars: fullData.avatars.slice(0, 8),
			talking_photos: fullData.talking_photos
		};

		return { data: trimmedData, error: null };
	} catch (error) {
		console.error("Error fetching HeyGen avatars:", error);
		return { data: null, error };
	}
};

export default getHeyGenAvatars;
