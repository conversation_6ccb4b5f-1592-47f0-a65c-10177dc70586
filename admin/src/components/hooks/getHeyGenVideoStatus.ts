import axios from "axios";
import { HeyGenVideoStatus } from "@src/types/heygen";

const getHeyGenVideoStatus = async (
    videoId: string
): Promise<{ data: HeyGenVideoStatus | null; error: any }> => {
    try {
        const response = await axios.request({
            //url: `https://api.heygen.com/v1/video_status/${videoId}`,
						//url: `https://api.heygen.com/v1/video_status.get?video_id=${videoId}`,
						url: `https://api.heygen.com/v2/avatar_group/b093d6cb4a70425db672a52233a87acb/avatars`,	
            method: "GET",
            headers: {
                Accept: "application/json",
                "X-Api-Key": "ODJmMDc0YzU2MGE1NGYzZWExOWM2ODE0ZDE1NTU0OTgtMTc1MjA4OTQ2Nw=="
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error fetching HeyGen video status:", error);
        return { data: null, error };
    }
};

export default getHeyGenVideoStatus;