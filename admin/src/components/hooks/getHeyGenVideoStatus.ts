import axios from "axios";
import { HeyGenVideoStatus } from "@src/types/heygen";

const getHeyGenVideoStatus = async (
    videoId: string,
		HEYGEN_API_KEY: string
): Promise<{ data: HeyGenVideoStatus | null; error: any }> => {
    try {
        const response = await axios.request({
						url: `https://api.heygen.com/v1/video_status.get?video_id=${videoId}`,
            method: "GET",
            headers: {
                Accept: "application/json",
                "X-Api-Key": HEYGEN_API_KEY
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error fetching HeyGen video status:", error);
        return { data: null, error };
    }
};

export default getHeyGenVideoStatus;